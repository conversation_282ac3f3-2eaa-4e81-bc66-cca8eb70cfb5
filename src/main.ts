import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'tdesign-vue-next/es/style/index.css'
import formCreate from '@form-create/tdesign'
import TDesign from 'tdesign-vue-next'
import FcDesigner from 'form-designer-x'

const app = createApp(App)

// 注册 UI 框架
app.use(TDesign)
app.use(ElementPlus)

// 注册 FormCreate 相关
app.use(formCreate)
// app.use(FcDesigner)

// 注册路由和状态管理
app.use(createPinia())
app.use(router)

app.mount('#app')

