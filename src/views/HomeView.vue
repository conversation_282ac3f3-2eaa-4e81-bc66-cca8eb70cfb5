<template>
  <t-affix ref="affix" :offset-bottom="20">
    <WifiIcon />
  </t-affix>

  <t-input v-model="input"></t-input>
  <el-button @click="startWorkflow">启动</el-button>
  <fc-designer id="formCreate" ref="designer" style="height: 90vh" :config="config"></fc-designer>
  <t-loading attach="#formCreate" size="small" :loading="loading"></t-loading>
</template>
<script setup lang="ts">
import { mastraClient } from '@/utlis/MastraClient'
import { WifiIcon } from 'tdesign-icons-vue-next'

import { onMounted, reactive, ref } from 'vue'
const workflow = mastraClient.getWorkflow('formCreateWorkflow')

const loading = ref(false)
const input = ref()

const designer = ref(null)

const FormItem = reactive({
  label: '测试',
  rule: [
    {
      type: 'input',
      field: 'username',
      title: '用户名',
      value: '',
    },
    {
      type: 'input',
      field: 'password',
      props: {
        type: 'password',
      },
      title: '密码',
      value: '',
    },
  ],
})
const config = reactive({
  componentRule: FormItem.rule,
  // useTemplate: true,
  // showMenuBar: false,
  // hiddenMenu: ['subform'],
})

async function startWorkflow() {
  console.log('workflows', workflow)
  if (!input.value) return
  const details = await workflow.details()

  console.log('details', details)
  loading.value = true
  const run = await workflow.createRun()

  const result = await workflow.startAsync({
    runId: run.runId,
    inputData: {
      input: input.value || '创建一个动物表单',
    },
  })
  console.log('result')
  if (result.status == 'success') {
    const jsonx = result.result?.output
    const jsonxData = JSON.parse(jsonx)
    console.log('result', jsonx)
    console.log('jsonxData', jsonxData)
    // formRules.value = jsonxData
    designer.value.setRule(jsonxData)
  }
  loading.value = false
}

onMounted(() => {
  if (designer.value) {
    designer.value.addMenu({
      name: 'shop',
      title: '业务组件',
    })
    designer.value.setRule(FormItem.rule)
  }
})
</script>
<style scoped lang="less"></style>
